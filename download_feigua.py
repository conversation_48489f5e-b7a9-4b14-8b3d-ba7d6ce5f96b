# 数据下载
import os
import xbot
import datetime
import asyncio
import time
import json

from xbot import print, sleep, web, win32
from .import package
from .package import variables as glv
from .logger import logger
from .utils import safe_find, safe_find_all, is_exist, get_feigua_competitor_file_name
from .fg_login import login
from .notify import notify

target_url = "https://dy.feigua.cn/app/#/brand-detail/index"


def downloadFeigua(brand, yesterday, target_date, folder):
    # 文件名
    file_name = get_feigua_competitor_file_name(brand["brand_name"], target_date)
    
    # 判断文件是否存在，都存在则直接返回
    if os.path.exists(os.path.join(folder, file_name)):
        return

    page = login("网易严选", target_url)

    time.sleep(10)


# 具体下载数据流程
def download_data(page, yesterday, target_date, folder, file_name):
    

    pass

# 选中特定日期
def select_date(page, target_date):
    [target_year, target_month, target_day] = target_date.split('-')
    target_month = target_month.lstrip('0')
    target_day = target_day.lstrip('0')

    more_date_ele = safe_find(page, "更多日期")

    for i in range(2):
        more_date_ele.hover()
        by_day_ele = safe_find(page, "自然日")
        if by_day_ele:
          break  
    
    by_day_ele.click()

    current_year_ele = safe_find(page, "当前年份")
    current_month_ele = safe_find(page, "当前月份")
    prev_month = safe_find(page, "前一月")
    next_month = safe_find(page, "后一月")
    prev_year = safe_find(page, "前一年")
    next_year = safe_find(page, "后一年")
    current_year_value = current_year_ele.get_text().replace("年", "")
    current_month_value = current_month_ele.get_text().replace("月", "")
    logger.info(f"当前年份: {current_year_value}, 当前月份: {current_month_value}")

    # 先选中月份
    while int(current_month_value) != int(target_month):
        if int(current_month_value) > int(target_month):
            prev_month.click()
        else:
            next_month.click()
        current_month_value = current_month_ele.get_text().replace("月", "")
    
    # 选中年份
    while int(current_year_value) != int(target_year):
        if int(current_year_value) > int(target_year):
            prev_year.click()
        else:
            next_year.click()
        current_year_value = current_year_ele.get_text().replace("年", "")
    
    # 选中日期
    day_list = safe_find_all(page, "日期按钮")
    for day in day_list:
        day_value = day.get_text()
        if day_value == target_day:
            day.click()
            break
    else:
        raise("未找到目标日期或目标日期不可选")

# 选择数据下载指标项
def select_metric(page):
    metric_btn = safe_find(page, "指标配置")
    metric_btn.click()
    metric_list = [
        '视频观看次数',
        '完播率',
        '平均观看时长',
        '商品曝光次数',
        '商品点击次数',
        '成交金额',
        '成交订单数',
        '成交人数',
        '退款金额',
        '商品曝光点击率(次数)',
        '商品点击成交率(次数)',
        '商品曝光成交率(次数)',
        '商品千次曝光成交',
        '引流直播间成交金额',
        '看后搜成交金额',
        '引流店铺页成交金额'
    ]
    # 选中指标
    current_not_select_list = safe_find_all(page, "未选中指标")
    for ele in current_not_select_list:
        if ele.get_text() in metric_list:
            ele.click()
    # 点击确定
    for i in range(2):
        confirm_btn = safe_find(page, "指标配置_确定btn")
        if confirm_btn:
            break
        else:
            modal_close_btn = safe_find(page, "智能助手_关闭btn")
            modal_close_btn.click()
            select_metric(page)
            return
    confirm_btn.click()

def main(args):
    # download('网易严选洗护清洁旗舰店', '2025-07-08')
    # page = web.get_active('chrome')
    # select_date(page, '2024-07-09')
    # select_metric(page)
    pass
